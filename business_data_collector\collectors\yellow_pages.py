"""
Yellow Pages scraper for business data using crawl4ai with LLM extraction.
"""
import asyncio
import logging
import json
import re
from typing import List, Dict, Any, Optional, Set
from urllib.parse import urljoin, quote_plus
from pydantic import BaseModel, Field
from crawl4ai import AsyncWebCrawler, BrowserConfig, CacheMode, CrawlerRunConfig, LLMExtractionStrategy, LLMConfig
from config import YELLOW_PAGES_DELAY

logger = logging.getLogger(__name__)


class BusinessData(BaseModel):
    """Pydantic model for business data extraction."""
    name: str = Field(..., description="The name of the business")
    address: str = Field(..., description="The full address of the business")
    phone_number: str = Field(..., description="The phone number of the business")
    website: str = Field(default="", description="The website URL of the business if available")
    description: str = Field(default="", description="A brief description of the business if available")


class YellowPagesCollector:
    """Collector for business data from Yellow Pages website using crawl4ai."""

    def __init__(self, llm_provider: str = "openai/gpt-4o-mini", api_key: Optional[str] = None):
        """Initialize the Yellow Pages collector.

        Args:
            llm_provider: LLM provider for extraction (e.g., "openai/gpt-4o-mini")
            api_key: API key for the LLM provider
        """
        self.base_url = "https://www.yellowpages.com"
        self.llm_provider = llm_provider
        self.api_key = api_key
        self.css_selector = ".result, .search-results .result, .organic .result, [data-listing-id], .listing"
        self.seen_names: Set[str] = set()

    def _get_browser_config(self) -> BrowserConfig:
        """Get browser configuration for crawl4ai."""
        return BrowserConfig(
            browser_type="chromium",
            headless=True,
            verbose=False,
        )

    def _get_llm_strategy(self) -> LLMExtractionStrategy:
        """Get LLM extraction strategy for business data."""
        instructions = """
        Extract business information from the Yellow Pages listings on this page.
        For each business listing, extract:
        - name: The business name
        - address: The full address including street, city, state
        - phone_number: The phone number (clean format)
        - website: The website URL if available (empty string if not found)
        - description: Any business description or category information if available

        Only extract businesses that have at least a name and either an address or phone number.
        Return the data as a JSON array of business objects.
        """

        llm_config = LLMConfig(
            provider=self.llm_provider,
            api_token=self.api_key
        )

        return LLMExtractionStrategy(
            llm_config=llm_config,
            schema=BusinessData.model_json_schema(),
            extraction_type="schema",
            instruction=instructions,
            input_format="markdown",
            verbose=False,
        )

    async def _check_no_results(self, crawler, url: str) -> bool:
        """Check if the page shows 'No Results Found'."""
        try:
            result = await crawler.arun(
                url=url,
                config=CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    session_id="yellowpages_check",
                ),
            )

            if result.success and result.cleaned_html:
                return "No Results Found" in result.cleaned_html

        except Exception as e:
            logger.debug(f"Error checking for no results: {str(e)}")

        return False

    def _is_duplicate(self, name: str) -> bool:
        """Check if business name is a duplicate."""
        normalized_name = name.lower().strip()
        return normalized_name in {n.lower().strip() for n in self.seen_names}
        
    def collect_businesses(self,
                          location: str = "Pittsburgh, PA",
                          categories: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Collect business data from Yellow Pages.

        Args:
            location: Location to search in
            categories: List of business categories to search for

        Returns:
            List of business data dictionaries
        """
        if categories is None:
            categories = [
                "restaurants",
                "retail stores",
                "auto repair",
                "beauty salons",
                "medical services",
                "professional services",
                "home services",
                "financial services"
            ]

        # Run async collection
        return asyncio.run(self._collect_businesses_async(location, categories))

    async def _collect_businesses_async(self, location: str, categories: List[str]) -> List[Dict[str, Any]]:
        """Async method to collect business data from Yellow Pages."""
        businesses = []

        # Skip if no API key provided
        if not self.api_key:
            logger.warning("No API key provided for LLM extraction. Skipping Yellow Pages.")
            return []

        browser_config = self._get_browser_config()

        async with AsyncWebCrawler(config=browser_config, verbose=False) as crawler:
            for category in categories:
                logger.info(f"Searching Yellow Pages for {category} in {location}...")

                try:
                    category_businesses = await self._search_category_async(crawler, category, location)
                    businesses.extend(category_businesses)

                    # Rate limiting - be respectful
                    await asyncio.sleep(YELLOW_PAGES_DELAY)

                except Exception as e:
                    logger.error(f"Error searching for {category}: {str(e)}")
                    continue

        logger.info(f"Collected {len(businesses)} businesses from Yellow Pages")
        return businesses
    
    async def _search_category_async(self, crawler, category: str, location: str, max_pages: int = 2) -> List[Dict[str, Any]]:
        """Search for businesses in a specific category using crawl4ai.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            max_pages: Maximum number of pages to scrape

        Returns:
            List of business data dictionaries
        """
        businesses = []

        for page in range(1, max_pages + 1):
            try:
                page_businesses = await self._scrape_search_page_async(crawler, category, location, page)
                businesses.extend(page_businesses)

                # If we got fewer results than expected, we've reached the end
                if len(page_businesses) < 5:  # Reduced threshold
                    break

                # Rate limiting between pages
                await asyncio.sleep(YELLOW_PAGES_DELAY)

            except Exception as e:
                logger.error(f"Error scraping page {page} for {category}: {str(e)}")
                break

        return businesses
    
    async def _scrape_search_page_async(self, crawler, category: str, location: str, page: int) -> List[Dict[str, Any]]:
        """Scrape a single search results page using crawl4ai with LLM extraction.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            page: Page number to scrape

        Returns:
            List of business data dictionaries
        """
        # Construct search URL
        search_url = f"{self.base_url}/search"
        params = {
            'search_terms': category,
            'geo_location_terms': location,
            'page': page
        }

        # Build full URL with parameters
        param_string = '&'.join([f"{k}={quote_plus(str(v))}" for k, v in params.items()])
        full_url = f"{search_url}?{param_string}"

        logger.debug(f"Scraping URL: {full_url}")

        try:
            # Check for "No Results Found" first
            no_results = await self._check_no_results(crawler, full_url)
            if no_results:
                logger.info(f"No more results found for {category} page {page}")
                return []

            # Get LLM strategy
            llm_strategy = self._get_llm_strategy()

            # Crawl with LLM extraction
            result = await crawler.arun(
                url=full_url,
                config=CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=llm_strategy,
                    css_selector=self.css_selector,
                    session_id=f"yellowpages_{category}_{location}",
                ),
            )

            if not result.success:
                logger.warning(f"Failed to crawl {full_url}: {result.error_message}")
                return []

            businesses = []

            # Parse extracted data
            if result.extracted_content:
                try:
                    extracted_data = json.loads(result.extracted_content)
                    logger.debug(f"Extracted data: {extracted_data}")

                    # Handle both list and single object responses
                    if isinstance(extracted_data, list):
                        business_list = extracted_data
                    else:
                        business_list = [extracted_data]

                    for business_data in business_list:
                        if isinstance(business_data, dict):
                            business = self._process_extracted_business(business_data)
                            if business and not self._is_duplicate(business['name']):
                                self.seen_names.add(business['name'])
                                businesses.append(business)

                except (json.JSONDecodeError, KeyError) as e:
                    logger.error(f"Error parsing extracted data: {str(e)}")

            logger.info(f"Extracted {len(businesses)} businesses from {category} page {page}")
            return businesses

        except Exception as e:
            logger.error(f"Error crawling {category} page {page}: {str(e)}")
            return []
    
    def _process_extracted_business(self, business_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process extracted business data from LLM extraction.

        Args:
            business_data: Raw extracted business data from LLM

        Returns:
            Processed business data dictionary or None if invalid
        """
        try:
            # Skip if error flag is present and True
            if business_data.get('error') is True:
                return None

            # Extract and clean business name
            name = business_data.get('name', '').strip()
            if not name:
                return None

            # Extract and clean address
            address = business_data.get('address', '').strip()

            # Extract and clean phone number
            phone = business_data.get('phone_number', business_data.get('phone', '')).strip()
            if phone:
                # Clean phone number
                phone = re.sub(r'[^\d\-\(\)\+\s]', '', phone)

            # Extract and clean website
            website = business_data.get('website', '').strip()
            if website and not website.startswith('http') and website:
                if website.startswith('www.'):
                    website = f"https://{website}"
                elif '.' in website:
                    website = f"https://{website}"
                else:
                    website = ""

            # Extract description and use as category
            description = business_data.get('description', '').strip()
            categories = [description] if description else []

            # Validate minimum requirements
            if not name or (not address and not phone):
                return None

            processed_business = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'types': categories,
                'primary_type': categories[0] if categories else '',
                'business_status': 'OPERATIONAL',
                'source': 'yellow_pages_llm'
            }

            return processed_business

        except Exception as e:
            logger.error(f"Error processing extracted business: {str(e)}")
            return None
