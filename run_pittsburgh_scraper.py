#!/usr/bin/env python3
"""
Run the Perfect Contact Extractor on Pittsburgh Business Data
Processes 1,341 business websites to extract emails and social media contacts.
"""

import asyncio
import pandas as pd
import sys
import os
from datetime import datetime

# Add the aganl directory to the path to import the extractor
sys.path.append(os.path.join(os.path.dirname(__file__), 'aganl'))

from perfect_contact_extractor import PerfectContactExtractor


def load_pittsburgh_urls():
    """Load URLs from the Pittsburgh businesses CSV."""
    print("📊 Loading Pittsburgh business data...")
    
    # Load the CSV
    df = pd.read_csv('business_data_collector/output/pittsburgh_businesses.csv')
    
    print(f"   • Total businesses: {len(df)}")
    print(f"   • Businesses with websites: {df['website'].notna().sum()}")
    
    # Filter for valid HTTP URLs
    valid_websites = df[df['website'].notna() & 
                       (df['website'] != '') & 
                       df['website'].str.startswith('http', na=False)]
    
    urls = valid_websites['website'].tolist()
    
    print(f"   • Valid HTTP URLs: {len(urls)}")
    print(f"   • Sample URLs:")
    for i, url in enumerate(urls[:5]):
        print(f"     {i+1}. {url}")
    
    return urls


async def run_pittsburgh_extraction():
    """Run the perfect contact extractor on Pittsburgh business URLs."""
    print("⭐ PITTSBURGH BUSINESS CONTACT EXTRACTION")
    print("=" * 60)
    
    # Load URLs
    urls = load_pittsburgh_urls()
    
    if not urls:
        print("❌ No URLs found to process!")
        return
    
    print(f"\n🚀 Starting extraction for {len(urls)} URLs...")
    
    # Configure extractor for production use
    # Using conservative settings for reliability
    extractor = PerfectContactExtractor(
        batch_size=25,      # Process 25 URLs per batch
        max_concurrent=5    # 5 concurrent requests per batch
    )
    
    # Start extraction
    start_time = datetime.now()
    
    try:
        results = await extractor.extract_perfect(urls)
        
        # Calculate duration
        duration = (datetime.now() - start_time).total_seconds()
        
        # Print comprehensive summary
        extractor.print_summary(results)
        
        # Export results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"aganl/pittsburgh_contacts_{timestamp}.csv"
        extractor.export_to_csv(results, filename)
        
        print(f"\n💾 Results exported to: {filename}")
        print(f"⚡ Processing rate: {len(urls)/duration:.2f} URLs/second")
        print(f"⏱️  Total time: {duration:.1f} seconds ({duration/60:.1f} minutes)")
        
        # Calculate projections for remaining work
        successful_results = [r for r in results if 'error' not in r]
        emails_found = len([r for r in successful_results if r.get('email')])
        socials_found = len([r for r in successful_results if r.get('social_media')])
        
        print(f"\n📈 EXTRACTION RESULTS:")
        print(f"   • Total URLs processed: {len(urls)}")
        print(f"   • Successful extractions: {len(successful_results)}")
        print(f"   • Emails found: {emails_found} ({emails_found/len(successful_results)*100:.1f}%)")
        print(f"   • Social media found: {socials_found} ({socials_found/len(successful_results)*100:.1f}%)")
        print(f"   • Both found: {len([r for r in successful_results if r.get('email') and r.get('social_media')])}")
        
        return results
        
    except Exception as e:
        print(f"❌ Extraction failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """Main function."""
    try:
        print("🎯 PITTSBURGH BUSINESS CONTACT EXTRACTOR")
        print("   Using the Perfect Contact Extractor for maximum accuracy")
        print("   Processing all 1,341 business websites from Pittsburgh data\n")
        
        results = await run_pittsburgh_extraction()
        
        if results:
            print(f"\n✅ EXTRACTION COMPLETED SUCCESSFULLY!")
            print("   Check the exported CSV file for detailed results.")
        else:
            print(f"\n❌ EXTRACTION FAILED!")
            
    except KeyboardInterrupt:
        print("\n\n⭐ Extraction interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Check if we're in the right directory
    if not os.path.exists('business_data_collector/output/pittsburgh_businesses.csv'):
        print("❌ Error: Pittsburgh businesses CSV not found!")
        print("   Make sure you're running this from the businesspipeline directory")
        sys.exit(1)
    
    if not os.path.exists('aganl/perfect_contact_extractor.py'):
        print("❌ Error: Perfect contact extractor not found!")
        print("   Make sure the aganl directory exists with perfect_contact_extractor.py")
        sys.exit(1)
    
    # Run the extraction
    asyncio.run(main())
