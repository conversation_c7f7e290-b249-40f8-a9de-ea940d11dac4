#!/usr/bin/env python3
"""
Analyze the Pittsburgh businesses CSV to count URLs
"""

import pandas as pd

def analyze_urls():
    # Load the CSV
    df = pd.read_csv('output/pittsburgh_businesses.csv')
    
    print(f"Total businesses: {len(df)}")
    print(f"Businesses with non-null websites: {df['website'].notna().sum()}")
    
    # Filter for non-empty websites
    non_empty_websites = df[df['website'].notna() & (df['website'] != '')]
    print(f"Businesses with non-empty websites: {len(non_empty_websites)}")
    
    # Check for HTTP URLs
    websites = non_empty_websites['website']
    http_urls = websites[websites.str.startswith('http', na=False)]
    print(f"Websites starting with http: {len(http_urls)}")
    
    print(f"\nSample websites:")
    for i, url in enumerate(http_urls.head(10)):
        print(f"  {i+1}. {url}")
    
    print(f"\nAll unique website values (first 20):")
    unique_websites = df['website'].dropna().unique()
    for i, url in enumerate(unique_websites[:20]):
        print(f"  {i+1}. '{url}'")
    
    return http_urls.tolist()

if __name__ == "__main__":
    urls = analyze_urls()
    print(f"\nTotal valid HTTP URLs found: {len(urls)}")
